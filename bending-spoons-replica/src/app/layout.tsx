import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Bending Spoons - Impossible. Maybe.",
  description: "We're a tech company that turns ideas into digital products used by millions of people worldwide. From AI-powered photo enhancement to productivity tools, we create impossible experiences.",
  keywords: "Bending Spoons, tech company, mobile apps, AI, photo enhancement, productivity, Remini, Evernote, Splice, Meetup",
  authors: [{ name: "Bending Spoons" }],
  creator: "Bending Spoons",
  publisher: "Bending Spoons",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://bendingspoons.com",
    title: "Bending Spoons - Impossible. Maybe.",
    description: "We're a tech company that turns ideas into digital products used by millions of people worldwide.",
    siteName: "Bending Spoons",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Bending Spoons - Impossible. Maybe.",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Bending Spoons - Impossible. Maybe.",
    description: "We're a tech company that turns ideas into digital products used by millions of people worldwide.",
    creator: "@bendingspoons",
    images: ["/og-image.jpg"],
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#3B82F6",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className="font-inter antialiased">
        {children}
      </body>
    </html>
  );
}
